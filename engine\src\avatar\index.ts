/**
 * 头像系统
 * 导出所有头像相关的组件和系统
 */

// 组件
export { FacialAnimationComponent, FacialExpressionType, VisemeType } from './components/FacialAnimationComponent';
export { LipSyncComponent } from './components/LipSyncComponent';
export { AIAnimationSynthesisComponent } from './components/AIAnimationSynthesisComponent';
export { FacialAnimationEditorComponent, EditorState } from './components/FacialAnimationEditorComponent';
export { AvatarComponent } from './components/AvatarComponent';

// 系统
export { FacialAnimationSystem } from './systems/FacialAnimationSystem';
export { FacialAnimationModelAdapterSystem } from './systems/FacialAnimationModelAdapterSystem';
export { LipSyncSystem } from './systems/LipSyncSystem';
export { AIAnimationSynthesisSystem } from './systems/AIAnimationSynthesisSystem';
export { FacialAnimationEditorSystem } from './systems/FacialAnimationEditorSystem';

// 虚拟化身定制系统
export { AvatarCustomizationSystem } from './AvatarCustomizationSystem';
export { FaceReconstructionSystem } from './FaceReconstructionSystem';
export { BodyParameterizationSystem } from './BodyParameterizationSystem';
export { ClothingSystem } from './ClothingSystem';
export { TextureGenerationSystem } from './TextureGenerationSystem';
export { AvatarPreviewSystem } from './AvatarPreviewSystem';
export { AvatarSaveSystem } from './AvatarSaveSystem';
export { AvatarSceneLoader } from './AvatarSceneLoader';
export { AvatarControlSystem } from './AvatarControlSystem';

// 导出类型
export type {
  AvatarCustomizationConfig,
  AvatarData,
  FaceData,
  FaceParameters,
  BodyData,
  BodyParameters,
  ClothingData,
  ClothingItem,
  ClothingType,
  TextureData
} from './AvatarCustomizationSystem';

export type {
  SaveConfig,
  SaveResult,
  AvatarSaveSystemConfig
} from './AvatarSaveSystem';

export type {
  SceneLoadConfig,
  SceneLoadResult,
  AvatarSceneLoaderConfig
} from './AvatarSceneLoader';

export type {
  ControlConfig,
  InputMapping,
  AvatarControlSystemConfig
} from './AvatarControlSystem';

export {
  MovementMode,
  InteractionType
} from './AvatarControlSystem';

// 适配器
export { FacialAnimationModelAdapterComponent, FacialAnimationModelType } from './adapters/FacialAnimationModelAdapter';

// 动画
export { FacialAnimationClip } from './animation/FacialAnimationClip';
export type { ExpressionKeyframe, VisemeKeyframe } from './animation/FacialAnimationClip';

// AI
export { EmotionBasedAnimationGenerator } from './ai/EmotionBasedAnimationGenerator';
export type { EmotionAnalysisResult } from './ai/EmotionBasedAnimationGenerator';
export { AIModel } from './ai/AIModel';
export type { AnimationGenerationRequest, AnimationGenerationResult } from './ai/AnimationGenerationTypes';
export { EmotionType } from './ai/AnimationGenerationTypes';

// 导航集成
export * from './navigation';
